"""
Comprehensive Medical Topics and Response Templates
This module contains extensive medical knowledge for enhanced chat comprehension
"""

# Medical Specialties Knowledge Base
MEDICAL_SPECIALTIES = {
    "emergency_medicine": {
        "common_presentations": [
            "chest pain", "shortness of breath", "abdominal pain", "trauma",
            "altered mental status", "seizures", "syncope", "poisoning"
        ],
        "triage_categories": ["immediate", "urgent", "less urgent", "non-urgent"],
        "protocols": ["ACLS", "ATLS", "PALS", "sepsis protocol"]
    },
    
    "internal_medicine": {
        "systems": [
            "cardiovascular", "respiratory", "gastrointestinal", "renal",
            "endocrine", "hematologic", "rheumatologic", "infectious"
        ],
        "chronic_conditions": [
            "diabetes", "hypertension", "COPD", "heart failure",
            "chronic kidney disease", "arthritis", "thyroid disorders"
        ]
    },
    
    "pediatrics": {
        "age_groups": ["neonate", "infant", "toddler", "preschool", "school-age", "adolescent"],
        "developmental_milestones": ["motor", "language", "social", "cognitive"],
        "common_issues": ["growth concerns", "feeding problems", "behavioral issues", "infections"]
    },
    
    "psychiatry": {
        "conditions": [
            "depression", "anxiety", "bipolar disorder", "schizophrenia",
            "PTSD", "OCD", "ADHD", "eating disorders", "substance abuse"
        ],
        "therapies": ["CBT", "DBT", "psychodynamic", "supportive", "group therapy"],
        "medications": ["antidepressants", "anxiolytics", "antipsychotics", "mood stabilizers"]
    },
    
    "obstetrics_gynecology": {
        "prenatal_care": ["trimester visits", "screening tests", "ultrasounds", "nutrition"],
        "gynecologic_issues": ["menstrual disorders", "contraception", "infections", "menopause"],
        "pregnancy_complications": ["gestational diabetes", "preeclampsia", "preterm labor"]
    },
    
    "orthopedics": {
        "injuries": ["fractures", "sprains", "strains", "dislocations", "tears"],
        "conditions": ["arthritis", "osteoporosis", "scoliosis", "tendinitis"],
        "treatments": ["casting", "physical therapy", "surgery", "injections"]
    },
    
    "dermatology": {
        "skin_conditions": ["acne", "eczema", "psoriasis", "rosacea", "skin cancer"],
        "hair_conditions": ["alopecia", "dandruff", "folliculitis"],
        "nail_conditions": ["fungal infections", "ingrown nails", "psoriasis"]
    },
    
    "cardiology": {
        "conditions": [
            "coronary artery disease", "arrhythmias", "heart failure",
            "valvular disease", "cardiomyopathy", "pericarditis"
        ],
        "tests": ["ECG", "echocardiogram", "stress test", "cardiac catheterization"],
        "interventions": ["medications", "pacemakers", "stents", "bypass surgery"]
    },
    
    "neurology": {
        "conditions": [
            "stroke", "epilepsy", "migraines", "Parkinson's", "Alzheimer's",
            "multiple sclerosis", "neuropathy", "myasthenia gravis"
        ],
        "symptoms": ["headaches", "dizziness", "weakness", "numbness", "tremors"],
        "tests": ["EEG", "EMG", "MRI", "lumbar puncture"]
    },
    
    "gastroenterology": {
        "conditions": [
            "GERD", "peptic ulcers", "IBD", "IBS", "hepatitis",
            "cirrhosis", "pancreatitis", "gallstones"
        ],
        "procedures": ["endoscopy", "colonoscopy", "ERCP", "liver biopsy"],
        "symptoms": ["abdominal pain", "nausea", "diarrhea", "constipation", "bleeding"]
    },
    
    "endocrinology": {
        "conditions": [
            "diabetes", "thyroid disorders", "adrenal disorders",
            "pituitary disorders", "metabolic syndrome", "osteoporosis"
        ],
        "hormones": ["insulin", "thyroid hormones", "cortisol", "growth hormone"],
        "tests": ["glucose tolerance", "thyroid function", "hormone levels"]
    },
    
    "pulmonology": {
        "conditions": [
            "asthma", "COPD", "pneumonia", "pulmonary embolism",
            "lung cancer", "sleep apnea", "pulmonary fibrosis"
        ],
        "tests": ["spirometry", "chest X-ray", "CT scan", "bronchoscopy"],
        "treatments": ["inhalers", "oxygen therapy", "pulmonary rehabilitation"]
    },
    
    "nephrology": {
        "conditions": [
            "chronic kidney disease", "acute kidney injury", "glomerulonephritis",
            "kidney stones", "polycystic kidney disease", "nephrotic syndrome"
        ],
        "treatments": ["medications", "dialysis", "transplant", "dietary management"],
        "tests": ["creatinine", "GFR", "urinalysis", "kidney biopsy"]
    },
    
    "oncology": {
        "cancer_types": [
            "breast", "lung", "colon", "prostate", "skin",
            "blood", "brain", "pancreatic", "ovarian"
        ],
        "treatments": ["chemotherapy", "radiation", "surgery", "immunotherapy", "targeted therapy"],
        "supportive_care": ["pain management", "nutrition", "psychological support"]
    },
    
    "infectious_diseases": {
        "pathogens": ["bacteria", "viruses", "fungi", "parasites"],
        "common_infections": ["respiratory", "urinary", "skin", "gastrointestinal", "sexually transmitted"],
        "prevention": ["vaccines", "hygiene", "prophylaxis", "isolation"]
    }
}

# Common Medical Scenarios and Responses
MEDICAL_SCENARIOS = {
    "acute_symptoms": {
        "chest_pain": {
            "red_flags": ["crushing pain", "radiation to arm/jaw", "shortness of breath", "sweating"],
            "differential": ["MI", "angina", "PE", "aortic dissection", "GERD", "musculoskeletal"],
            "initial_assessment": "Evaluate cardiac risk factors, character of pain, associated symptoms",
            "urgent_action": "If cardiac suspected: Call 911, chew aspirin if not allergic, rest"
        },
        
        "severe_headache": {
            "red_flags": ["thunderclap onset", "fever", "neurological deficits", "vision changes"],
            "differential": ["subarachnoid hemorrhage", "meningitis", "migraine", "cluster", "tension"],
            "initial_assessment": "Onset, severity, associated symptoms, previous history",
            "urgent_action": "If red flags present: Immediate emergency evaluation needed"
        },
        
        "abdominal_pain": {
            "red_flags": ["severe pain", "rigid abdomen", "fever", "vomiting blood"],
            "differential": ["appendicitis", "cholecystitis", "pancreatitis", "obstruction", "perforation"],
            "initial_assessment": "Location, character, duration, associated symptoms",
            "urgent_action": "If severe or with red flags: Emergency evaluation"
        },
        
        "difficulty_breathing": {
            "red_flags": ["sudden onset", "chest pain", "cyanosis", "altered mental status"],
            "differential": ["asthma", "COPD exacerbation", "pneumonia", "PE", "heart failure"],
            "initial_assessment": "Onset, triggers, associated symptoms, medical history",
            "urgent_action": "If severe: Call 911, sit upright, use rescue inhaler if prescribed"
        }
    },
    
    "chronic_conditions": {
        "diabetes_management": {
            "key_points": ["blood sugar monitoring", "medication adherence", "diet", "exercise"],
            "complications": ["neuropathy", "retinopathy", "nephropathy", "cardiovascular"],
            "monitoring": ["HbA1c every 3 months", "annual eye exam", "foot checks"],
            "lifestyle": ["carbohydrate counting", "regular meals", "physical activity"]
        },
        
        "hypertension": {
            "key_points": ["medication compliance", "lifestyle changes", "monitoring"],
            "complications": ["stroke", "heart disease", "kidney disease"],
            "lifestyle": ["DASH diet", "reduce sodium", "exercise", "weight loss", "stress management"],
            "monitoring": ["home BP monitoring", "regular check-ups"]
        },
        
        "depression": {
            "key_points": ["medication", "therapy", "lifestyle", "support system"],
            "warning_signs": ["suicidal thoughts", "self-harm", "severe functional impairment"],
            "treatments": ["antidepressants", "psychotherapy", "exercise", "mindfulness"],
            "support": ["crisis hotlines", "support groups", "family involvement"]
        }
    },
    
    "preventive_care": {
        "vaccinations": {
            "children": ["routine childhood series", "annual flu", "COVID-19"],
            "adults": ["annual flu", "COVID-19", "tetanus booster", "shingles if >50"],
            "special_populations": ["pregnancy", "immunocompromised", "travel"]
        },
        
        "screening": {
            "cancer": ["mammogram", "colonoscopy", "cervical cancer", "prostate", "skin"],
            "cardiovascular": ["blood pressure", "cholesterol", "diabetes"],
            "other": ["osteoporosis", "vision", "hearing", "dental"]
        },
        
        "lifestyle": {
            "diet": ["balanced nutrition", "portion control", "limit processed foods"],
            "exercise": ["150 min moderate activity/week", "strength training 2x/week"],
            "habits": ["no smoking", "limit alcohol", "adequate sleep", "stress management"]
        }
    },
    
    "medication_guidance": {
        "safe_use": {
            "principles": ["follow prescriptions", "don't share medications", "check interactions"],
            "storage": ["proper temperature", "away from children", "check expiration"],
            "adherence": ["use reminders", "understand purpose", "report side effects"]
        },
        
        "common_medications": {
            "pain_relief": {
                "acetaminophen": "Safe for most, avoid in liver disease, max 3g/day",
                "ibuprofen": "Take with food, avoid if kidney issues or bleeding risk",
                "aspirin": "Blood thinner effect, avoid if bleeding risk"
            },
            "antibiotics": {
                "principles": "Complete full course, don't save leftovers, only for bacterial infections",
                "side_effects": "Diarrhea, yeast infections, allergic reactions"
            }
        }
    },
    
    "mental_health": {
        "anxiety": {
            "coping_strategies": ["deep breathing", "progressive relaxation", "mindfulness", "exercise"],
            "when_to_seek_help": ["interferes with daily life", "panic attacks", "avoidance behaviors"],
            "treatments": ["therapy (CBT)", "medications", "lifestyle changes"]
        },
        
        "stress_management": {
            "techniques": ["time management", "exercise", "hobbies", "social support"],
            "warning_signs": ["physical symptoms", "mood changes", "sleep problems"],
            "resources": ["counseling", "stress reduction programs", "apps"]
        }
    },
    
    "women_health": {
        "pregnancy": {
            "prenatal_care": ["regular visits", "vitamins", "avoid harmful substances"],
            "warning_signs": ["bleeding", "severe pain", "decreased fetal movement"],
            "lifestyle": ["nutrition", "exercise", "avoid alcohol/smoking"]
        },
        
        "menopause": {
            "symptoms": ["hot flashes", "mood changes", "sleep issues"],
            "management": ["hormone therapy", "lifestyle changes", "alternative therapies"],
            "health_maintenance": ["bone density", "cardiovascular health"]
        }
    },
    
    "pediatric_care": {
        "common_illnesses": {
            "fever": "Monitor temperature, fluids, rest, acetaminophen/ibuprofen as directed",
            "ear_infections": "Common in children, may need antibiotics, pain management",
            "rashes": "Many causes, monitor for other symptoms, most are benign"
        },
        
        "development": {
            "milestones": "Track but remember variation is normal",
            "concerns": "Early intervention if delays, discuss with pediatrician",
            "safety": "Childproofing, car seats, supervision"
        }
    },
    
    "senior_health": {
        "common_issues": {
            "falls": "Remove hazards, exercise for strength, vision checks",
            "polypharmacy": "Regular medication reviews, one pharmacy",
            "cognitive": "Memory screening, safety measures, support"
        },
        
        "healthy_aging": {
            "physical": "Stay active, strength training, balance exercises",
            "mental": "Social engagement, learning, hobbies",
            "preventive": "Regular check-ups, vaccinations, screenings"
        }
    }
}

# Response Enhancement Templates
RESPONSE_TEMPLATES = {
    "empathetic_openings": [
        "I understand you're experiencing {symptom}, and I'm here to help guide you through this.",
        "Thank you for sharing your concerns about {issue}. Let's explore this together.",
        "I can see this is causing you worry. Let me provide some helpful information.",
        "Your health concerns are important, and I'm glad you're seeking guidance.",
        "I appreciate you reaching out about {concern}. Let's discuss what might be happening."
    ],
    
    "safety_first": [
        "Your safety is my top priority. Based on what you've described...",
        "Before we proceed, I need to ensure you're safe. Are you experiencing...",
        "Given your symptoms, it's important to rule out serious conditions first.",
        "Let's first address any immediate health risks before exploring other possibilities."
    ],
    
    "educational_bridges": [
        "Let me explain what might be happening in your body...",
        "To help you understand better, here's what this condition involves...",
        "Many people aren't aware that...",
        "It's helpful to know that..."
    ],
    
    "action_plans": [
        "Here's what I recommend you do next:",
        "Let's create a plan to address your concerns:",
        "Based on our discussion, these are the steps you can take:",
        "Moving forward, here's how you can manage this:"
    ],
    
    "reassurance": [
        "While concerning, many of these symptoms can be effectively managed.",
        "You're taking the right steps by seeking information and care.",
        "Remember, with proper treatment, most people see significant improvement.",
        "It's encouraging that you're being proactive about your health."
    ],
    
    "closing_support": [
        "Please don't hesitate to seek immediate care if symptoms worsen.",
        "Remember, I'm here to support your health journey.",
        "Take care of yourself, and follow up with your healthcare provider.",
        "Your health matters, and seeking help is always the right choice."
    ]
}

# Conversation Flow Patterns
CONVERSATION_PATTERNS = {
    "initial_assessment": [
        "Tell me more about when this started",
        "What makes it better or worse?",
        "Have you experienced this before?",
        "Are there any other symptoms?",
        "Rate the severity from 1-10"
    ],
    
    "clarification": [
        "Can you describe the sensation in more detail?",
        "When you say {symptom}, do you mean...",
        "Help me understand exactly where you feel this",
        "Has the pattern changed recently?"
    ],
    
    "medical_history": [
        "Do you have any chronic conditions?",
        "What medications are you currently taking?",
        "Any allergies I should know about?",
        "Is there a family history of...",
        "Have you had any recent procedures?"
    ],
    
    "lifestyle_factors": [
        "Tell me about your daily routine",
        "How's your stress level lately?",
        "What does your diet typically include?",
        "How much physical activity do you get?",
        "How well have you been sleeping?"
    ],
    
    "psychosocial": [
        "How is this affecting your daily life?",
        "Do you have support at home?",
        "Are there any particular stressors right now?",
        "How are you coping emotionally?"
    ]
}

# Medical Terminology Simplification
MEDICAL_TERMS_SIMPLIFIED = {
    "hypertension": "high blood pressure",
    "hypotension": "low blood pressure",
    "tachycardia": "fast heart rate",
    "bradycardia": "slow heart rate",
    "dyspnea": "difficulty breathing",
    "edema": "swelling",
    "syncope": "fainting",
    "vertigo": "dizziness with spinning sensation",
    "pyrexia": "fever",
    "myalgia": "muscle pain",
    "arthralgia": "joint pain",
    "cephalgia": "headache",
    "epistaxis": "nosebleed",
    "hematemesis": "vomiting blood",
    "hematuria": "blood in urine",
    "dysuria": "painful urination",
    "polyuria": "excessive urination",
    "oliguria": "decreased urination",
    "anorexia": "loss of appetite",
    "nausea": "feeling sick to stomach",
    "emesis": "vomiting",
    "constipation": "difficulty passing stool",
    "diarrhea": "loose, watery stools",
    "pruritus": "itching",
    "erythema": "redness",
    "ecchymosis": "bruising",
    "alopecia": "hair loss",
    "insomnia": "difficulty sleeping",
    "somnolence": "excessive sleepiness",
    "malaise": "general feeling of illness",
    "asthenia": "weakness",
    "anemia": "low red blood cells",
    "leukocytosis": "high white blood cells",
    "thrombocytopenia": "low platelets"
}

def get_specialty_response(specialty: str, scenario: str) -> dict:
    """Get specialized response based on medical specialty and scenario"""
    if specialty in MEDICAL_SPECIALTIES and scenario in MEDICAL_SCENARIOS:
        return {
            "specialty_info": MEDICAL_SPECIALTIES[specialty],
            "scenario_guidance": MEDICAL_SCENARIOS[scenario]
        }
    return {}

def simplify_medical_terms(text: str) -> str:
    """Replace medical jargon with simpler terms"""
    simplified = text
    for medical_term, simple_term in MEDICAL_TERMS_SIMPLIFIED.items():
        simplified = simplified.replace(medical_term, f"{medical_term} ({simple_term})")
    return simplified

def get_response_template(template_type: str) -> list:
    """Get appropriate response templates"""
    return RESPONSE_TEMPLATES.get(template_type, [])

def get_conversation_prompts(stage: str) -> list:
    """Get conversation prompts for different stages"""
    return CONVERSATION_PATTERNS.get(stage, [])