#!/usr/bin/env python3
"""
Create a sample PDF with lab results for testing
"""

from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.units import inch
import os

def create_sample_lab_pdf():
    """Create a sample PDF with lab results"""
    
    filename = "sample_lab_results.pdf"
    
    # Create PDF
    doc = SimpleDocTemplate(filename, pagesize=letter)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title = Paragraph("LABORATÓRIO CLÍNICO - RESULTADOS DE EXAMES", styles['Title'])
    story.append(title)
    story.append(Spacer(1, 12))
    
    # Patient info
    patient_info = """
    <b>Paciente:</b> <PERSON><br/>
    <b>Data de Nascimento:</b> 15/03/1980<br/>
    <b>Data do Exame:</b> 25/06/2025<br/>
    <b>Médico Solicitante:</b> <PERSON><PERSON> <PERSON>
    """
    story.append(Paragraph(patient_info, styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Lab results
    lab_results = """
    <b>HEMOGRAMA COMPLETO</b><br/>
    Hemoglobina: 14.2 g/dL<br/>
    Hematócrito: 42%<br/>
    Eritrócitos: 4.5 milhões/mm³<br/>
    Leucócitos: 7.500/mm³<br/>
    Plaquetas: 280.000/mm³<br/>
    <br/>
    <b>BIOQUÍMICA SÉRICA</b><br/>
    Glicose: 92 mg/dL<br/>
    Creatinina: 0.9 mg/dL<br/>
    Uréia: 28 mg/dL<br/>
    Colesterol Total: 185 mg/dL<br/>
    HDL Colesterol: 55 mg/dL<br/>
    LDL Colesterol: 110 mg/dL<br/>
    Triglicérides: 100 mg/dL<br/>
    <br/>
    <b>FUNÇÃO HEPÁTICA</b><br/>
    ALT (TGP): 25 U/L<br/>
    AST (TGO): 22 U/L<br/>
    Bilirrubina Total: 0.8 mg/dL<br/>
    <br/>
    <b>FUNÇÃO TIREOIDIANA</b><br/>
    TSH: 2.1 mUI/L<br/>
    T4 Livre: 1.2 ng/dL<br/>
    T3 Livre: 3.1 pg/mL<br/>
    """
    
    story.append(Paragraph(lab_results, styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Footer
    footer = """
    <b>Observações:</b><br/>
    Todos os valores estão dentro dos parâmetros de normalidade.<br/>
    Resultado liberado em: 25/06/2025 às 14:30<br/>
    <br/>
    <i>Este documento é válido apenas com assinatura digital do responsável técnico.</i>
    """
    story.append(Paragraph(footer, styles['Normal']))
    
    # Build PDF
    doc.build(story)
    
    print(f"✅ PDF criado: {filename}")
    return filename

def create_abnormal_lab_pdf():
    """Create a sample PDF with abnormal lab results"""
    
    filename = "sample_abnormal_lab_results.pdf"
    
    # Create PDF
    doc = SimpleDocTemplate(filename, pagesize=letter)
    styles = getSampleStyleSheet()
    story = []
    
    # Title
    title = Paragraph("LABORATÓRIO CLÍNICO - RESULTADOS DE EXAMES", styles['Title'])
    story.append(title)
    story.append(Spacer(1, 12))
    
    # Patient info
    patient_info = """
    <b>Paciente:</b> Maria Oliveira<br/>
    <b>Data de Nascimento:</b> 22/08/1975<br/>
    <b>Data do Exame:</b> 25/06/2025<br/>
    <b>Médico Solicitante:</b> Dr. Carlos Mendes
    """
    story.append(Paragraph(patient_info, styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Lab results with abnormal values
    lab_results = """
    <b>HEMOGRAMA COMPLETO</b><br/>
    Hemoglobina: 9.2 g/dL <font color="red">(BAIXO)</font><br/>
    Hematócrito: 28% <font color="red">(BAIXO)</font><br/>
    Eritrócitos: 3.2 milhões/mm³<br/>
    Leucócitos: 15.000/mm³ <font color="red">(ALTO)</font><br/>
    Plaquetas: 120.000/mm³ <font color="red">(BAIXO)</font><br/>
    <br/>
    <b>BIOQUÍMICA SÉRICA</b><br/>
    Glicose: 180 mg/dL <font color="red">(ALTO)</font><br/>
    Creatinina: 2.1 mg/dL <font color="red">(ALTO)</font><br/>
    Uréia: 65 mg/dL <font color="red">(ALTO)</font><br/>
    Colesterol Total: 250 mg/dL <font color="red">(ALTO)</font><br/>
    HDL Colesterol: 30 mg/dL <font color="red">(BAIXO)</font><br/>
    LDL Colesterol: 180 mg/dL <font color="red">(ALTO)</font><br/>
    Triglicérides: 200 mg/dL <font color="red">(ALTO)</font><br/>
    <br/>
    <b>FUNÇÃO HEPÁTICA</b><br/>
    ALT (TGP): 85 U/L <font color="red">(ALTO)</font><br/>
    AST (TGO): 78 U/L <font color="red">(ALTO)</font><br/>
    Bilirrubina Total: 2.5 mg/dL <font color="red">(ALTO)</font><br/>
    <br/>
    <b>FUNÇÃO TIREOIDIANA</b><br/>
    TSH: 8.5 mUI/L <font color="red">(ALTO)</font><br/>
    T4 Livre: 0.6 ng/dL <font color="red">(BAIXO)</font><br/>
    T3 Livre: 1.8 pg/mL <font color="red">(BAIXO)</font><br/>
    """
    
    story.append(Paragraph(lab_results, styles['Normal']))
    story.append(Spacer(1, 20))
    
    # Footer
    footer = """
    <b>Observações:</b><br/>
    <font color="red"><b>ATENÇÃO: Múltiplos valores alterados detectados.</b></font><br/>
    Recomenda-se avaliação médica urgente.<br/>
    Resultado liberado em: 25/06/2025 às 16:45<br/>
    <br/>
    <i>Este documento é válido apenas com assinatura digital do responsável técnico.</i>
    """
    story.append(Paragraph(footer, styles['Normal']))
    
    # Build PDF
    doc.build(story)
    
    print(f"✅ PDF criado: {filename}")
    return filename

if __name__ == "__main__":
    print("📄 Criando PDFs de exemplo para teste...")
    
    try:
        normal_pdf = create_sample_lab_pdf()
        abnormal_pdf = create_abnormal_lab_pdf()
        
        print(f"\n✅ PDFs criados com sucesso!")
        print(f"📄 Normal: {normal_pdf}")
        print(f"📄 Alterado: {abnormal_pdf}")
        print(f"\n💡 Use estes arquivos para testar o upload de PDF na aplicação Streamlit.")
        
    except Exception as e:
        print(f"❌ Erro ao criar PDFs: {str(e)}")
        import traceback
        traceback.print_exc()
