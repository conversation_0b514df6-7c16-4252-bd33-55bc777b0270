#!/usr/bin/env python3
"""
Test script for date extraction functionality
"""

from ai_labs_formatter import <PERSON><PERSON>ab<PERSON><PERSON>att<PERSON>

def test_date_extraction():
    """Test date extraction from different text formats"""
    
    print("🗓️ Testing Date Extraction")
    print("=" * 50)
    
    formatter = AILabFormatter()
    
    test_cases = [
        {
            "name": "Data no formato brasileiro",
            "text": """
            LABORATÓRIO CLÍNICO
            Data do exame: 15/03/2024
            
            HEMOGRAMA COMPLETO
            Hemoglobina: 14.2 g/dL
            Hematócrito: 42%
            """
        },
        {
            "name": "Data com traços",
            "text": """
            EXAMES LABORATORIAIS
            Realizado em: 22-08-2024
            
            BIOQUÍMICA SÉRICA
            Glicose: 92 mg/dL
            Creatinina: 0.9 mg/dL
            """
        },
        {
            "name": "Data com pontos",
            "text": """
            RESULTADOS DE EXAMES
            Coletado: 10.12.2024
            
            HEMOGRAMA
            Leucócitos: 7.500/mm³
            Plaquetas: 280.000/mm³
            """
        },
        {
            "name": "Data no meio do texto",
            "text": """
            HEMOGRAMA COMPLETO
            Hemoglobina: 13.8 g/dL
            Exame realizado em 05/07/2024
            Hematócrito: 41%
            """
        },
        {
            "name": "Sem data específica",
            "text": """
            HEMOGRAMA COMPLETO
            Hemoglobina: 14.2 g/dL
            Hematócrito: 42%
            """
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 30)
        
        # Extract date
        extracted_date = formatter.parser.extract_date_from_text(test_case['text'])
        print(f"📅 Data extraída: {extracted_date}")
        
        # Process and format
        results = formatter.process_input(text=test_case['text'])
        print(f"📋 Resultado formatado:")
        print(f"   {results.formatted_text}")

if __name__ == "__main__":
    test_date_extraction()
