<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LabsForm Pro - Formatação de Exames Médicos</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/jspdf@2.5.1/dist/jspdf.umd.min.js"></script>
    <style>
        .medical-card {
            transition: all 0.3s ease;
        }
        .medical-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .dropzone {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .dropzone.dragover {
            border-color: #4299e1;
            background-color: #ebf8ff;
        }
        .result-textarea {
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }
        .alert-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fcd34d 100%);
        }
        .processing-spinner {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .pulse-success {
            animation: pulseSuccess 0.6s ease-in-out;
        }
        @keyframes pulseSuccess {
            0% { background-color: #10b981; }
            50% { background-color: #34d399; }
            100% { background-color: #10b981; }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-gradient-to-r from-slate-600 to-slate-700 text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-microscope text-2xl"></i>
                    <h1 class="text-xl font-medium">
                        Fluxograma <span class="text-sm opacity-80">(Pro)</span>
                    </h1>
                </div>
                <div class="text-center">
                    <h2 class="text-2xl font-light">LabsForm Pro</h2>
                    <p class="text-sm opacity-90">Formatação Automática de Exames</p>
                </div>
                <div class="flex items-center space-x-2 text-sm">
                    <i class="fas fa-shield-alt"></i>
                    <span>Seguro & Privado</span>
                </div>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- Título da Seção -->
        <div class="text-center mb-8">
            <h3 class="text-3xl font-bold text-gray-800 mb-2">Formatar Exames</h3>
            <p class="text-gray-600">Cole texto ou faça upload de PDFs para formatação automática</p>
        </div>

        <!-- Métodos de Input -->
        <div class="grid md:grid-cols-2 gap-8 mb-8">
            <!-- Copy-Paste Method -->
            <div class="medical-card bg-white rounded-lg shadow-lg p-6">
                <div class="mb-4">
                    <h4 class="text-xl font-semibold text-gray-800 mb-2 flex items-center">
                        <i class="fas fa-clipboard text-blue-600 mr-2"></i>
                        Método Copy-Paste
                    </h4>
                    <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
                        <p class="text-sm text-blue-800">
                            <strong>Instruções:</strong> Na intranet do laboratório, pressione 
                            <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl+A</kbd> e 
                            <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl+C</kbd>, 
                            depois <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl+V</kbd> no campo abaixo:
                        </p>
                    </div>
                </div>
                <textarea 
                    id="textInput" 
                    class="w-full h-40 p-4 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none transition-colors"
                    placeholder="Cole aqui o conteúdo dos exames laboratoriais...&#10;&#10;Exemplo:&#10;HEMOGRAMA COMPLETO&#10;Hemoglobina: 14.2 g/dL&#10;Hematócrito: 42%&#10;Leucócitos: 7.500/mm³"
                ></textarea>
                <button 
                    id="processTextBtn" 
                    class="mt-4 w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
                >
                    <i class="fas fa-cogs mr-2"></i>
                    Processar Texto
                </button>
            </div>

            <!-- File Upload Method -->
            <div class="medical-card bg-white rounded-lg shadow-lg p-6">
                <div class="mb-4">
                    <h4 class="text-xl font-semibold text-gray-800 mb-2 flex items-center">
                        <i class="fas fa-file-upload text-green-600 mr-2"></i>
                        Upload de Arquivos
                    </h4>
                    <p class="text-sm text-gray-600 mb-4">
                        Suporte para PDFs e imagens (PNG, JPG) - máximo 10MB por arquivo
                    </p>
                </div>
                <div 
                    id="dropzone" 
                    class="dropzone border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-blue-400 transition-colors"
                >
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600 mb-2">Arraste arquivos aqui ou clique para selecionar</p>
                    <p class="text-sm text-gray-500">PDF, PNG, JPG (máx. 10MB cada)</p>
                    <input type="file" id="fileInput" multiple accept=".pdf,.png,.jpg,.jpeg" class="hidden">
                </div>
                <div id="fileList" class="mt-4 space-y-2"></div>
                <button 
                    id="processFilesBtn" 
                    class="mt-4 w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
                    disabled
                >
                    <i class="fas fa-file-medical mr-2"></i>
                    Processar Arquivos
                </button>
            </div>
        </div>

        <!-- Processing Indicator -->
        <div id="processingIndicator" class="hidden bg-white rounded-lg shadow-lg p-6 mb-8 text-center">
            <div class="flex items-center justify-center mb-4">
                <i class="fas fa-spinner processing-spinner text-3xl text-blue-600 mr-3"></i>
                <span class="text-lg font-semibold text-gray-800">Processando exames...</span>
            </div>
            <div class="bg-gray-200 rounded-full h-2">
                <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
            </div>
            <p class="text-sm text-gray-600 mt-2">Analisando e formatando resultados...</p>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="hidden fade-in">
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-xl font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-clipboard-check text-green-600 mr-2"></i>
                        Resultado Formatado
                    </h4>
                    <div class="flex space-x-2">
                        <button 
                            id="copyBtn" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
                        >
                            <i class="fas fa-copy mr-2"></i>
                            Copiar
                        </button>
                        <button 
                            id="exportPdfBtn" 
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
                        >
                            <i class="fas fa-file-pdf mr-2"></i>
                            Exportar PDF
                        </button>
                        <button 
                            id="clearBtn" 
                            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
                        >
                            <i class="fas fa-trash mr-2"></i>
                            Limpar
                        </button>
                    </div>
                </div>
                <textarea 
                    id="resultOutput" 
                    class="result-textarea w-full h-64 p-4 bg-gray-50 border-2 border-gray-300 rounded-lg focus:border-green-500 focus:outline-none transition-colors text-sm"
                    readonly
                    placeholder="O resultado formatado aparecerá aqui..."
                ></textarea>
            </div>

            <!-- Valores Alterados -->
            <div id="alertsSection" class="hidden bg-white rounded-lg shadow-lg p-6 mb-8">
                <h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                    Valores Fora da Referência
                </h4>
                <div id="alertsList" class="space-y-2"></div>
            </div>
        </div>

        <!-- Instruções e Alertas -->
        <div class="alert-box rounded-lg shadow-lg p-6 border border-yellow-300">
            <div class="flex items-start">
                <i class="fas fa-lightbulb text-3xl text-yellow-700 mr-4 mt-1"></i>
                <div>
                    <h4 class="text-xl font-bold text-yellow-800 mb-4">ATENÇÃO!</h4>
                    <ul class="text-yellow-800 space-y-2 text-sm leading-relaxed">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-yellow-600 mr-2 mt-1 text-xs"></i>
                            Sempre revise se todos os exames e resultados estão incluídos no resultado formatado
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-yellow-600 mr-2 mt-1 text-xs"></i>
                            Sempre confira se todos os valores estão corretos e foram interpretados adequadamente
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-user-md text-yellow-600 mr-2 mt-1 text-xs"></i>
                            Lembre-se: uma evolução adequada é sua responsabilidade profissional - revise sempre!
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-envelope text-yellow-600 mr-2 mt-1 text-xs"></i>
                            Envie sugestões e erros encontrados para: 
                            <a href="mailto:<EMAIL>" class="underline hover:text-yellow-900"><EMAIL></a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Histórico de Exames -->
        <div id="historySection" class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-history text-purple-600 mr-2"></i>
                    Histórico de Processamentos
                </h4>
                <button 
                    id="clearHistoryBtn" 
                    class="text-red-600 hover:text-red-700 text-sm flex items-center"
                >
                    <i class="fas fa-trash-alt mr-1"></i>
                    Limpar Histórico
                </button>
            </div>
            <div id="historyList" class="space-y-3">
                <p class="text-gray-500 text-center py-8">Nenhum exame processado ainda.</p>
            </div>
        </div>
    </main>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <script>
        class LabsFormPro {
            constructor() {
                this.history = JSON.parse(localStorage.getItem('labsFormHistory') || '[]');
                this.referenceValues = {
                    hemoglobina: { min: 12.0, max: 15.5, unit: 'g/dL' },
                    hematocrito: { min: 36, max: 46, unit: '%' },
                    leucocitos: { min: 4000, max: 11000, unit: '/mm³' },
                    plaquetas: { min: 150000, max: 450000, unit: '/mm³' },
                    glicose: { min: 70, max: 99, unit: 'mg/dL' },
                    creatinina: { min: 0.6, max: 1.3, unit: 'mg/dL' },
                    ureia: { min: 15, max: 45, unit: 'mg/dL' },
                    colesterol: { min: 0, max: 200, unit: 'mg/dL' }
                };
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadHistory();
            }

            setupEventListeners() {
                // Text processing
                document.getElementById('processTextBtn').addEventListener('click', () => this.processText());
                document.getElementById('textInput').addEventListener('input', (e) => {
                    const hasText = e.target.value.trim().length > 0;
                    document.getElementById('processTextBtn').disabled = !hasText;
                });

                // File upload
                const dropzone = document.getElementById('dropzone');
                const fileInput = document.getElementById('fileInput');
                
                dropzone.addEventListener('click', () => fileInput.click());
                dropzone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    dropzone.classList.add('dragover');
                });
                dropzone.addEventListener('dragleave', () => {
                    dropzone.classList.remove('dragover');
                });
                dropzone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    dropzone.classList.remove('dragover');
                    this.handleFiles(e.dataTransfer.files);
                });
                
                fileInput.addEventListener('change', (e) => this.handleFiles(e.target.files));
                document.getElementById('processFilesBtn').addEventListener('click', () => this.processFiles());

                // Result actions
                document.getElementById('copyBtn').addEventListener('click', () => this.copyResult());
                document.getElementById('exportPdfBtn').addEventListener('click', () => this.exportToPDF());
                document.getElementById('clearBtn').addEventListener('click', () => this.clearResults());
                document.getElementById('clearHistoryBtn').addEventListener('click', () => this.clearHistory());
            }

            handleFiles(files) {
                const fileList = document.getElementById('fileList');
                const processBtn = document.getElementById('processFilesBtn');
                
                fileList.innerHTML = '';
                
                Array.from(files).forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'flex items-center justify-between bg-gray-50 p-3 rounded-lg';
                    fileItem.innerHTML = `
                        <div class="flex items-center">
                            <i class="fas fa-file-${file.type.includes('pdf') ? 'pdf' : 'image'} text-red-600 mr-2"></i>
                            <span class="text-sm font-medium">${file.name}</span>
                            <span class="text-xs text-gray-500 ml-2">(${(file.size / 1024 / 1024).toFixed(1)} MB)</span>
                        </div>
                        <button class="text-red-600 hover:text-red-700" onclick="this.parentElement.remove(); this.updateProcessBtn()">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    fileList.appendChild(fileItem);
                });

                processBtn.disabled = files.length === 0;
            }

            async processText() {
                const text = document.getElementById('textInput').value.trim();
                if (!text) return;

                this.showProcessing();
                
                // Simulate processing delay
                await this.delay(1500);
                
                const result = this.parseLabResults(text);
                this.displayResults(result);
                this.saveToHistory(result, 'texto');
            }

            async processFiles() {
                const fileInput = document.getElementById('fileInput');
                if (!fileInput.files.length) return;

                this.showProcessing();
                
                // Simulate file processing
                await this.delay(2500);
                
                // For demo purposes, we'll use sample text to simulate OCR results
                const sampleText = `
                    HEMOGRAMA COMPLETO
                    Hemoglobina: 13.8 g/dL
                    Hematócrito: 41%
                    Eritrócitos: 4.5 milhões/mm³
                    Leucócitos: 6.800/mm³
                    Plaquetas: 280.000/mm³
                    
                    BIOQUÍMICA SÉRICA
                    Glicose: 92 mg/dL
                    Creatinina: 0.9 mg/dL
                    Uréia: 28 mg/dL
                    Colesterol Total: 185 mg/dL
                `;
                
                const result = this.parseLabResults(sampleText);
                this.displayResults(result);
                this.saveToHistory(result, 'arquivo');
            }

            parseLabResults(text) {
                const patterns = {
                    hemograma: {
                        hemoglobina: /(?:hemoglobina|hb)[:\s]*([0-9,.\s]+)(?:\s*g\/dl)/gi,
                        hematocrito: /(?:hematócrito|ht)[:\s]*([0-9,.\s]+)%/gi,
                        eritrocitos: /(?:eritrócitos|hemácias)[:\s]*([0-9,.\s]+)(?:\s*milhões\/mm³)/gi,
                        leucocitos: /(?:leucócitos)[:\s]*([0-9,.]+)(?:\s*\/mm³)/gi,
                        plaquetas: /(?:plaquetas)[:\s]*([0-9,.]+)(?:\s*\/mm³)/gi
                    },
                    bioquimica: {
                        glicose: /(?:glicose)[:\s]*([0-9,.\s]+)(?:\s*mg\/dl)/gi,
                        creatinina: /(?:creatinina)[:\s]*([0-9,.\s]+)(?:\s*mg\/dl)/gi,
                        ureia: /(?:uréia|ureia)[:\s]*([0-9,.\s]+)(?:\s*mg\/dl)/gi,
                        colesterol: /(?:colesterol total)[:\s]*([0-9,.\s]+)(?:\s*mg\/dl)/gi
                    }
                };

                const results = {
                    hemograma: {},
                    bioquimica: {},
                    formatted: '',
                    alerts: [],
                    rawText: text
                };

                // Parse each category
                Object.keys(patterns).forEach(category => {
                    Object.keys(patterns[category]).forEach(test => {
                        const matches = text.match(patterns[category][test]);
                        if (matches && matches.length > 0) {
                            const value = this.extractValue(matches[0]);
                            results[category][test] = value;
                            this.checkReferenceValue(test, value, results.alerts);
                        }
                    });
                });

                results.formatted = this.formatResults(results);
                return results;
            }

            extractValue(match) {
                return match.split(/[:\s]+/).pop().trim().replace(',', '.');
            }

            checkReferenceValue(test, value, alerts) {
                if (this.referenceValues[test]) {
                    const ref = this.referenceValues[test];
                    const numValue = parseFloat(value);
                    
                    if (!isNaN(numValue)) {
                        if (numValue < ref.min) {
                            alerts.push({
                                test: test,
                                value: value,
                                status: 'baixo',
                                reference: `${ref.min}-${ref.max}`,
                                unit: ref.unit
                            });
                        } else if (numValue > ref.max) {
                            alerts.push({
                                test: test,
                                value: value,
                                status: 'alto',
                                reference: `${ref.min}-${ref.max}`,
                                unit: ref.unit
                            });
                        }
                    }
                }
            }

            formatResults(results) {
                let formatted = [];

                // Hemograma
                if (Object.keys(results.hemograma).length > 0) {
                    const hemo = results.hemograma;
                    let line = 'HEMOGRAMA: ';
                    const parts = [];
                    
                    if (hemo.hemoglobina) parts.push(`Hb ${hemo.hemoglobina}g/dL`);
                    if (hemo.hematocrito) parts.push(`Ht ${hemo.hematocrito}%`);
                    if (hemo.eritrocitos) parts.push(`Hem ${hemo.eritrocitos}`);
                    if (hemo.leucocitos) parts.push(`Leuc ${hemo.leucocitos}`);
                    if (hemo.plaquetas) parts.push(`Plaq ${hemo.plaquetas}`);
                    
                    line += parts.join(' / ');
                    formatted.push(line);
                }

                // Bioquímica
                if (Object.keys(results.bioquimica).length > 0) {
                    const bio = results.bioquimica;
                    let line = 'BIOQUÍMICA: ';
                    const parts = [];
                    
                    if (bio.glicose) parts.push(`Gli ${bio.glicose}mg/dL`);
                    if (bio.creatinina) parts.push(`Cr ${bio.creatinina}mg/dL`);
                    if (bio.ureia) parts.push(`Ureia ${bio.ureia}mg/dL`);
                    if (bio.colesterol) parts.push(`CT ${bio.colesterol}mg/dL`);
                    
                    line += parts.join(' / ');
                    formatted.push(line);
                }

                return formatted.join('\n');
            }

            displayResults(results) {
                document.getElementById('processingIndicator').classList.add('hidden');
                document.getElementById('resultsSection').classList.remove('hidden');
                document.getElementById('resultOutput').value = results.formatted;

                // Display alerts
                if (results.alerts.length > 0) {
                    const alertsSection = document.getElementById('alertsSection');
                    const alertsList = document.getElementById('alertsList');
                    
                    alertsList.innerHTML = '';
                    results.alerts.forEach(alert => {
                        const alertDiv = document.createElement('div');
                        alertDiv.className = `p-3 rounded-lg border-l-4 ${
                            alert.status === 'alto' ? 'bg-red-50 border-red-500' : 'bg-blue-50 border-blue-500'
                        }`;
                        alertDiv.innerHTML = `
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle ${
                                    alert.status === 'alto' ? 'text-red-600' : 'text-blue-600'
                                } mr-2"></i>
                                <span class="font-semibold">${alert.test.toUpperCase()}:</span>
                                <span class="ml-2">${alert.value} ${alert.unit} - ${alert.status.toUpperCase()}</span>
                                <span class="ml-2 text-gray-600">(ref: ${alert.reference} ${alert.unit})</span>
                            </div>
                        `;
                        alertsList.appendChild(alertDiv);
                    });
                    
                    alertsSection.classList.remove('hidden');
                } else {
                    document.getElementById('alertsSection').classList.add('hidden');
                }
            }

            async showProcessing() {
                document.getElementById('processingIndicator').classList.remove('hidden');
                const progressBar = document.getElementById('progressBar');
                
                for (let i = 0; i <= 100; i += 10) {
                    progressBar.style.width = i + '%';
                    await this.delay(150);
                }
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            async copyResult() {
                const result = document.getElementById('resultOutput').value;
                try {
                    await navigator.clipboard.writeText(result);
                    this.showToast('Resultado copiado para a área de transferência!', 'success');
                    
                    // Visual feedback
                    const btn = document.getElementById('copyBtn');
                    btn.classList.add('pulse-success');
                    setTimeout(() => btn.classList.remove('pulse-success'), 600);
                } catch (err) {
                    this.showToast('Erro ao copiar resultado', 'error');
                }
            }

            exportToPDF() {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                // Header
                doc.setFontSize(18);
                doc.text('Resumo de Exames Laboratoriais', 20, 20);
                
                doc.setFontSize(12);
                doc.text(`Processado em: ${new Date().toLocaleString('pt-BR')}`, 20, 35);
                doc.text('Gerado por: LabsForm Pro', 20, 45);
                
                // Content
                const result = document.getElementById('resultOutput').value;
                const lines = doc.splitTextToSize(result, 170);
                doc.setFontSize(10);
                doc.text(lines, 20, 60);
                
                // Footer
                doc.setFontSize(8);
                doc.text('IMPORTANTE: Sempre revise os resultados antes do uso clínico', 20, 280);
                
                doc.save(`exames_${new Date().toISOString().split('T')[0]}.pdf`);
                this.showToast('PDF exportado com sucesso!', 'success');
            }

            clearResults() {
                document.getElementById('resultsSection').classList.add('hidden');
                document.getElementById('textInput').value = '';
                document.getElementById('fileInput').value = '';
                document.getElementById('fileList').innerHTML = '';
                document.getElementById('processTextBtn').disabled = true;
                document.getElementById('processFilesBtn').disabled = true;
            }

            saveToHistory(result, type) {
                const entry = {
                    id: Date.now(),
                    type: type,
                    result: result.formatted,
                    timestamp: new Date().toISOString(),
                    preview: result.formatted.substring(0, 100) + '...',
                    alerts: result.alerts.length
                };
                
                this.history.unshift(entry);
                this.history = this.history.slice(0, 20); // Keep only last 20
                localStorage.setItem('labsFormHistory', JSON.stringify(this.history));
                this.loadHistory();
            }

            loadHistory() {
                const historyList = document.getElementById('historyList');
                
                if (this.history.length === 0) {
                    historyList.innerHTML = '<p class="text-gray-500 text-center py-8">Nenhum exame processado ainda.</p>';
                    return;
                }
                
                historyList.innerHTML = '';
                this.history.forEach(entry => {
                    const historyItem = document.createElement('div');
                    historyItem.className = 'bg-gray-50 p-4 rounded-lg border hover:bg-gray-100 transition-colors cursor-pointer';
                    historyItem.innerHTML = `
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-${entry.type === 'arquivo' ? 'file' : 'clipboard'} text-gray-600 mr-2"></i>
                                    <span class="font-medium text-gray-800">${entry.type === 'arquivo' ? 'Arquivo' : 'Texto'}</span>
                                    ${entry.alerts > 0 ? `<span class="ml-2 bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">${entry.alerts} alerta(s)</span>` : ''}
                                </div>
                                <p class="text-sm text-gray-600 mb-2">${entry.preview}</p>
                                <p class="text-xs text-gray-500">${new Date(entry.timestamp).toLocaleString('pt-BR')}</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-700 ml-4" onclick="labsForm.loadFromHistory('${entry.id}')">
                                <i class="fas fa-redo"></i>
                            </button>
                        </div>
                    `;
                    historyList.appendChild(historyItem);
                });
            }

            loadFromHistory(id) {
                const entry = this.history.find(item => item.id.toString() === id);
                if (entry) {
                    document.getElementById('resultOutput').value = entry.result;
                    document.getElementById('resultsSection').classList.remove('hidden');
                    this.showToast('Resultado carregado do histórico!', 'info');
                }
            }

            clearHistory() {
                this.history = [];
                localStorage.removeItem('labsFormHistory');
                this.loadHistory();
                this.showToast('Histórico limpo!', 'info');
            }

            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                const colors = {
                    success: 'bg-green-500',
                    error: 'bg-red-500',
                    info: 'bg-blue-500'
                };
                
                toast.className = `${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
                toast.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'} mr-2"></i>
                        ${message}
                    </div>
                `;
                
                document.getElementById('toastContainer').appendChild(toast);
                
                setTimeout(() => toast.classList.remove('translate-x-full'), 100);
                setTimeout(() => {
                    toast.classList.add('translate-x-full');
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize the application when DOM is fully loaded
            window.labsForm = new LabsFormPro();
            
            // Set up event listeners for the main buttons
            document.getElementById('themeToggle').addEventListener('click', () => labsForm.toggleTheme());
            document.getElementById('helpBtn').addEventListener('click', () => labsForm.showHelp());
            
            // Initialize tooltips
            const tooltips = document.querySelectorAll('[data-tooltip]');
            tooltips.forEach(tooltip => {
                new Tooltip(tooltip, {
                    placement: tooltip.dataset.placement || 'top',
                    title: tooltip.dataset.tooltip
                });
            });
            
            // Check for saved theme preference
            const savedTheme = localStorage.getItem('labsFormTheme');
            if (savedTheme === 'dark') {
                labsForm.toggleTheme();
            }
            
            // Show welcome message
            labsForm.showToast('Bem-vindo ao LabsForm Pro!', 'info');
        });
    </script>
</body>
</html>
