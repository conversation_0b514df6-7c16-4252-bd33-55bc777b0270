# 🧪 AI Labs Formatter Pro

## Formatação Inteligente de Exames Laboratoriais com IA

Uma aplicação avançada em Python que utiliza Inteligência Artificial para processar, formatar e analisar resultados de exames laboratoriais de forma automática e inteligente.

## 🌟 Características Principais

### 🤖 Inteligência Artificial Integrada
- **Análise com Claude AI**: Interpretação inteligente dos resultados
- **Resumo clínico automatizado**: Análise contextual dos valores
- **Recomendações personalizadas**: Sugestões baseadas nos resultados
- **Detecção de padrões**: Identificação automática de alterações significativas

### 📋 Processamento Avançado
- **Múltiplos formatos de entrada**: Texto, PDF, imagens (PNG/JPG)
- **OCR integrado**: Extração de texto de imagens com Tesseract
- **Parser inteligente**: Reconhecimento automático de valores e unidades
- **Formatação padronizada**: Saída em formato clínico profissional

### 🔬 Análise Laboratorial Completa
- **Hemograma completo**: Hemoglobina, hematócrito, leucócitos, plaquetas
- **Bioquímica sérica**: Glicose, creatinina, ureia, perfil lipídico
- **Função hepática**: ALT, AST, bilirrubinas
- **Função tireoidiana**: TSH, T3, T4
- **Valores de referência**: Base de dados completa com ranges normais

### 🎯 Interface Moderna
- **Streamlit UI**: Interface web responsiva e intuitiva
- **Design profissional**: Layout médico com cores e ícones apropriados
- **Alertas visuais**: Destaque automático para valores alterados
- **Exportação**: Cópia fácil e geração de relatórios

## 🚀 Instalação e Configuração

### Pré-requisitos
```bash
Python 3.9+
pip (gerenciador de pacotes Python)
```

### 1. Clone ou baixe os arquivos
```bash
# Arquivos necessários:
# - ai_labs_formatter.py
# - requirements_labs.txt
# - test_labs_formatter.py (opcional)
```

### 2. Instale as dependências
```bash
pip install -r requirements_labs.txt
```

### 3. Instale modelos de linguagem
```bash
# Modelo português para spaCy
python -m spacy download pt_core_news_sm

# Dados NLTK (executado automaticamente)
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet')"
```

### 4. Configure a API do Anthropic (opcional)
```bash
# Para funcionalidades avançadas de IA
export ANTHROPIC_API_KEY="sua_api_key_aqui"
```

## 🎮 Como Usar

### Interface Web (Recomendado)
```bash
streamlit run ai_labs_formatter.py
```
Acesse: `http://localhost:8501`

### Teste via Linha de Comando
```bash
python test_labs_formatter.py
```

## 📊 Funcionalidades Detalhadas

### 1. Entrada de Dados
- **Texto direto**: Cole resultados da intranet do laboratório
- **Upload PDF**: Extração automática de texto de arquivos PDF
- **Upload de imagens**: OCR para processar fotos de exames

### 2. Processamento Inteligente
```python
# Exemplo de uso programático
from ai_labs_formatter import AILabFormatter

formatter = AILabFormatter(api_key="sua_api_key")
results = formatter.process_input(text="Hemoglobina: 14.2 g/dL...")

print(results.formatted_text)  # Resultado formatado
print(results.alerts)          # Valores alterados
print(results.summary)         # Análise de IA
```

### 3. Saída Formatada
```
HEMOGRAMA: Hb 14.2g/dL / Ht 42% / Leuc 7.500 / Plaq 280.000
BIOQUÍMICA: Gli 92mg/dL / Cr 0.9mg/dL / Ureia 28mg/dL
```

### 4. Alertas Automáticos
- 🔴 **Valores altos**: Destacados em vermelho
- 🔵 **Valores baixos**: Destacados em azul
- ✅ **Valores normais**: Confirmação visual

## 🔧 Configuração Avançada

### Valores de Referência Personalizados
```python
# Edite a classe ReferenceRanges em ai_labs_formatter.py
RANGES = {
    'hemoglobina': {'min': 12.0, 'max': 15.5, 'unit': 'g/dL'},
    # Adicione novos testes aqui
}
```

### Padrões de Reconhecimento
```python
# Adicione novos padrões regex na classe LabTextParser
patterns['novo_teste'] = re.compile(r'(?:novo_teste)[:\s]*([0-9,.\s]+)', re.IGNORECASE)
```

## 📁 Estrutura do Projeto

```
ai_labs_formatter.py          # Aplicação principal
test_labs_formatter.py        # Script de testes
requirements_labs.txt         # Dependências
create_sample_pdf.py         # Gerador de PDFs de exemplo
sample_lab_results.pdf       # PDF de exemplo (normal)
sample_abnormal_lab_results.pdf  # PDF de exemplo (alterado)
README_AI_LABS.md           # Esta documentação
```

## 🧪 Exemplos de Uso

### Texto de Entrada
```
HEMOGRAMA COMPLETO
Hemoglobina: 14.2 g/dL
Hematócrito: 42%
Leucócitos: 7.500/mm³

BIOQUÍMICA SÉRICA
Glicose: 92 mg/dL
Creatinina: 0.9 mg/dL
```

### Saída Formatada
```
HEMOGRAMA: Hb 14.2g/dL / Ht 42% / Leuc 7.500
BIOQUÍMICA: Gli 92mg/dL / Cr 0.9mg/dL
```

### Análise de IA (com API key)
```
RESUMO CLÍNICO:
Exames laboratoriais dentro da normalidade, indicando 
bom estado geral de saúde...

RECOMENDAÇÕES:
Manter hábitos saudáveis e acompanhamento médico regular...
```

## ⚠️ Avisos Importantes

### Uso Médico
- ⚠️ **Esta ferramenta é AUXILIAR** - não substitui avaliação médica
- 🏥 **Sempre consulte um médico** para interpretação adequada
- 📋 **Revise todos os resultados** antes do uso clínico
- 🔒 **Mantenha a confidencialidade** dos dados dos pacientes

### Limitações Técnicas
- OCR pode ter imprecisões em imagens de baixa qualidade
- Reconhecimento limitado aos padrões programados
- IA requer conexão com internet e API key válida
- Valores de referência podem variar entre laboratórios

## 🛠️ Solução de Problemas

### Erro de OCR
```bash
# Instale Tesseract
# macOS: brew install tesseract
# Ubuntu: sudo apt-get install tesseract-ocr
# Windows: Baixe do GitHub oficial
```

### Erro de spaCy
```bash
python -m spacy download pt_core_news_sm
```

### Erro de API
```bash
# Verifique se a API key está configurada
echo $ANTHROPIC_API_KEY
```

## 📞 Suporte

Para dúvidas, sugestões ou relatos de bugs:
- 📧 Email: <EMAIL>
- 🐛 Issues: Reporte problemas técnicos
- 💡 Sugestões: Ideias para melhorias

## 📄 Licença

Este projeto é fornecido "como está" para fins educacionais e de auxílio médico. 
Use com responsabilidade e sempre sob supervisão médica adequada.

---

**🏥 Desenvolvido para auxiliar profissionais de saúde na formatação e análise de exames laboratoriais**
