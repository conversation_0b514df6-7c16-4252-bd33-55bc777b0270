#!/usr/bin/env python3
"""
Test script for AI Labs Formatter
Demonstrates the functionality without Streamlit UI
"""

from ai_labs_formatter import AILabFormatter, ReferenceRanges
import os

def test_basic_functionality():
    """Test basic lab formatting functionality"""
    
    print("🧪 Testing AI Labs Formatter")
    print("=" * 50)
    
    # Sample lab results text
    sample_text = """
    HEMOGRAMA COMPLETO
    Hemoglobina: 13.8 g/dL
    Hematócrito: 41%
    Eritrócitos: 4.5 milhões/mm³
    Leucócitos: 6.800/mm³
    Plaquetas: 280.000/mm³
    
    BIOQUÍMICA SÉRICA
    Glicose: 92 mg/dL
    Creatinina: 0.9 mg/dL
    Uréia: 28 mg/dL
    Colesterol Total: 185 mg/dL
    
    FUNÇÃO HEPÁTICA
    ALT (TGP): 25 U/L
    AST (TGO): 22 U/L
    Bilirrubina Total: 0.8 mg/dL
    
    TIREOIDE
    TSH: 2.1 mUI/L
    T4 Livre: 1.2 ng/dL
    T3 Livre: 3.1 pg/mL
    """
    
    # Initialize formatter (without API key for basic testing)
    formatter = AILabFormatter()
    
    print("📝 Texto de entrada:")
    print(sample_text)
    print("\n" + "=" * 50)
    
    # Process the text
    print("🔄 Processando resultados...")
    results = formatter.process_input(text=sample_text)
    
    print("\n📋 RESULTADO FORMATADO:")
    print("-" * 30)
    print(results.formatted_text)
    
    if results.alerts:
        print("\n⚠️ VALORES ALTERADOS:")
        print("-" * 30)
        for alert in results.alerts:
            status_emoji = "🔴" if alert['status'] == 'high' else "🔵"
            print(f"{status_emoji} {alert['test']}: {alert['value']} - {alert['status'].upper()}")
            print(f"   Referência: {alert['reference']}")
    else:
        print("\n✅ Todos os valores dentro da normalidade!")
    
    print(f"\n🤖 ANÁLISE DE IA:")
    print("-" * 30)
    print(f"Resumo: {results.summary}")
    print(f"\nRecomendações: {results.recommendations}")
    
    print(f"\n📊 DADOS DETALHADOS:")
    print("-" * 30)
    for result in results.raw_data:
        status_emoji = "✅" if result.status == "normal" else ("🔴" if result.status == "high" else "🔵")
        print(f"{status_emoji} {result.test_name}: {result.value} {result.unit} ({result.status})")

def test_reference_ranges():
    """Test reference ranges functionality"""
    
    print("\n\n🔬 Testing Reference Ranges")
    print("=" * 50)
    
    test_cases = [
        ("hemoglobina", 14.2),
        ("glicose", 110),  # High
        ("creatinina", 0.5),  # Low
        ("leucocitos", 7500),
        ("tsh", 5.0),  # High
    ]
    
    for test_name, value in test_cases:
        ref = ReferenceRanges.get_reference(test_name)
        status = ReferenceRanges.check_status(test_name, value)
        
        if ref:
            status_emoji = "✅" if status == "normal" else ("🔴" if status == "high" else "🔵")
            print(f"{status_emoji} {test_name.title()}: {value} {ref['unit']} - {status.upper()}")
            print(f"   Referência: {ref['min']}-{ref['max']} {ref['unit']}")
        else:
            print(f"❓ {test_name.title()}: Referência não encontrada")

def test_with_ai():
    """Test with AI functionality (requires API key)"""
    
    print("\n\n🤖 Testing AI Functionality")
    print("=" * 50)
    
    api_key = os.getenv("ANTHROPIC_API_KEY")
    
    if not api_key:
        print("⚠️ ANTHROPIC_API_KEY não encontrada no ambiente.")
        print("Para testar a funcionalidade de IA, configure a variável de ambiente:")
        print("export ANTHROPIC_API_KEY='sua_api_key_aqui'")
        return
    
    # Sample with abnormal values
    abnormal_text = """
    HEMOGRAMA COMPLETO
    Hemoglobina: 9.2 g/dL
    Hematócrito: 28%
    Leucócitos: 15.000/mm³
    Plaquetas: 120.000/mm³
    
    BIOQUÍMICA SÉRICA
    Glicose: 180 mg/dL
    Creatinina: 2.1 mg/dL
    """
    
    formatter = AILabFormatter(api_key=api_key)
    results = formatter.process_input(text=abnormal_text)
    
    print("📝 Texto com valores alterados:")
    print(abnormal_text)
    
    print("\n📋 RESULTADO FORMATADO:")
    print("-" * 30)
    print(results.formatted_text)
    
    print(f"\n🤖 ANÁLISE DETALHADA DE IA:")
    print("-" * 30)
    print(f"Resumo: {results.summary}")
    print(f"\nRecomendações: {results.recommendations}")

if __name__ == "__main__":
    try:
        test_basic_functionality()
        test_reference_ranges()
        test_with_ai()
        
        print("\n\n🎉 Todos os testes concluídos!")
        print("=" * 50)
        print("Para usar a interface web, execute:")
        print("streamlit run ai_labs_formatter.py")
        
    except Exception as e:
        print(f"\n❌ Erro durante os testes: {str(e)}")
        import traceback
        traceback.print_exc()
