#!/usr/bin/env python3
"""
AI-Powered Lab Results Formatter
Advanced medical lab results processing with AI integration
"""

import streamlit as st
import pandas as pd
import numpy as np
import re
import json
import os
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from anthropic import Anthropic
import PyPDF2
import pytesseract
from PIL import Image
import io
import base64
from textblob import TextBlob
import nltk
import spacy

# Download required NLTK data
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('wordnet', quiet=True)
except:
    pass

# Load spaCy model for Portuguese
try:
    nlp = spacy.load("pt_core_news_sm")
except:
    try:
        os.system("python -m spacy download pt_core_news_sm")
        nlp = spacy.load("pt_core_news_sm")
    except:
        # Fallback to English model
        nlp = spacy.load("en_core_web_sm")

@dataclass
class LabResult:
    """Data class for lab results"""
    test_name: str
    value: str
    unit: str
    reference_range: str
    status: str = "normal"  # normal, high, low, critical
    category: str = ""

@dataclass
class ProcessedResults:
    """Data class for processed lab results"""
    formatted_text: str
    alerts: List[Dict]
    raw_data: List[LabResult]
    summary: str
    recommendations: str

class ReferenceRanges:
    """Medical reference ranges for common lab tests"""
    
    RANGES = {
        # Hemograma
        'hemoglobina': {'min': 12.0, 'max': 15.5, 'unit': 'g/dL', 'category': 'hemograma'},
        'hematocrito': {'min': 36, 'max': 46, 'unit': '%', 'category': 'hemograma'},
        'eritrocitos': {'min': 4.0, 'max': 5.5, 'unit': 'milhões/mm³', 'category': 'hemograma'},
        'leucocitos': {'min': 4000, 'max': 11000, 'unit': '/mm³', 'category': 'hemograma'},
        'plaquetas': {'min': 150000, 'max': 450000, 'unit': '/mm³', 'category': 'hemograma'},
        
        # Bioquímica
        'glicose': {'min': 70, 'max': 99, 'unit': 'mg/dL', 'category': 'bioquimica'},
        'creatinina': {'min': 0.6, 'max': 1.3, 'unit': 'mg/dL', 'category': 'bioquimica'},
        'ureia': {'min': 15, 'max': 45, 'unit': 'mg/dL', 'category': 'bioquimica'},
        'colesterol_total': {'min': 0, 'max': 200, 'unit': 'mg/dL', 'category': 'bioquimica'},
        'hdl': {'min': 40, 'max': 999, 'unit': 'mg/dL', 'category': 'bioquimica'},
        'ldl': {'min': 0, 'max': 130, 'unit': 'mg/dL', 'category': 'bioquimica'},
        'triglicerides': {'min': 0, 'max': 150, 'unit': 'mg/dL', 'category': 'bioquimica'},
        
        # Função hepática
        'alt_tgp': {'min': 7, 'max': 56, 'unit': 'U/L', 'category': 'hepatico'},
        'ast_tgo': {'min': 10, 'max': 40, 'unit': 'U/L', 'category': 'hepatico'},
        'bilirrubina_total': {'min': 0.2, 'max': 1.2, 'unit': 'mg/dL', 'category': 'hepatico'},
        
        # Tireoide
        'tsh': {'min': 0.4, 'max': 4.0, 'unit': 'mUI/L', 'category': 'tireoide'},
        't4_livre': {'min': 0.8, 'max': 1.8, 'unit': 'ng/dL', 'category': 'tireoide'},
        't3_livre': {'min': 2.3, 'max': 4.2, 'unit': 'pg/mL', 'category': 'tireoide'},
    }
    
    @classmethod
    def get_reference(cls, test_name: str) -> Optional[Dict]:
        """Get reference range for a test"""
        return cls.RANGES.get(test_name.lower().replace(' ', '_'))
    
    @classmethod
    def check_status(cls, test_name: str, value: float) -> str:
        """Check if value is normal, high, or low"""
        ref = cls.get_reference(test_name)
        if not ref:
            return "unknown"
        
        if value < ref['min']:
            return "low"
        elif value > ref['max']:
            return "high"
        else:
            return "normal"

class LabTextParser:
    """Advanced text parser for lab results"""
    
    def __init__(self):
        self.patterns = self._create_patterns()
    
    def _create_patterns(self) -> Dict[str, re.Pattern]:
        """Create regex patterns for different lab tests"""
        patterns = {}
        
        # Common patterns for lab values
        value_pattern = r'([0-9]+[.,]?[0-9]*)'
        
        # Hemograma patterns
        patterns['hemoglobina'] = re.compile(rf'(?:hemoglobina|hb)[:\s]*{value_pattern}(?:\s*g/dl)', re.IGNORECASE)
        patterns['hematocrito'] = re.compile(rf'(?:hematócrito|ht)[:\s]*{value_pattern}(?:\s*%)', re.IGNORECASE)
        patterns['eritrocitos'] = re.compile(rf'(?:eritrócitos|hemácias)[:\s]*{value_pattern}(?:\s*milhões/mm³)', re.IGNORECASE)
        patterns['leucocitos'] = re.compile(rf'(?:leucócitos)[:\s]*{value_pattern}(?:\s*/mm³)', re.IGNORECASE)
        patterns['plaquetas'] = re.compile(rf'(?:plaquetas)[:\s]*{value_pattern}(?:\s*/mm³)', re.IGNORECASE)
        
        # Bioquímica patterns
        patterns['glicose'] = re.compile(rf'(?:glicose)[:\s]*{value_pattern}(?:\s*mg/dl)', re.IGNORECASE)
        patterns['creatinina'] = re.compile(rf'(?:creatinina)[:\s]*{value_pattern}(?:\s*mg/dl)', re.IGNORECASE)
        patterns['ureia'] = re.compile(rf'(?:uréia|ureia)[:\s]*{value_pattern}(?:\s*mg/dl)', re.IGNORECASE)
        patterns['colesterol_total'] = re.compile(rf'(?:colesterol total)[:\s]*{value_pattern}(?:\s*mg/dl)', re.IGNORECASE)
        
        return patterns
    
    def parse_text(self, text: str) -> List[LabResult]:
        """Parse text and extract lab results"""
        results = []
        
        for test_name, pattern in self.patterns.items():
            matches = pattern.findall(text)
            if matches:
                for match in matches:
                    value_str = match.replace(',', '.')
                    try:
                        value_float = float(value_str)
                        ref = ReferenceRanges.get_reference(test_name)
                        
                        if ref:
                            status = ReferenceRanges.check_status(test_name, value_float)
                            ref_range = f"{ref['min']}-{ref['max']} {ref['unit']}"
                            
                            result = LabResult(
                                test_name=test_name.replace('_', ' ').title(),
                                value=value_str,
                                unit=ref['unit'],
                                reference_range=ref_range,
                                status=status,
                                category=ref['category']
                            )
                            results.append(result)
                    except ValueError:
                        continue
        
        return results

class AILabFormatter:
    """AI-powered lab results formatter using Claude"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("ANTHROPIC_API_KEY")
        if self.api_key:
            self.client = Anthropic(api_key=self.api_key)
        else:
            self.client = None
        
        self.parser = LabTextParser()
    
    def extract_text_from_pdf(self, pdf_file) -> str:
        """Extract text from PDF file"""
        try:
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            return text
        except Exception as e:
            st.error(f"Erro ao extrair texto do PDF: {str(e)}")
            return ""
    
    def extract_text_from_image(self, image_file) -> str:
        """Extract text from image using OCR"""
        try:
            image = Image.open(image_file)
            text = pytesseract.image_to_string(image, lang='por')
            return text
        except Exception as e:
            st.error(f"Erro ao extrair texto da imagem: {str(e)}")
            return ""
    
    def get_ai_analysis(self, text: str, lab_results: List[LabResult]) -> Dict[str, str]:
        """Get AI analysis of lab results"""
        if not self.client:
            return {
                "summary": "Análise de IA não disponível (API key não configurada)",
                "recommendations": "Configure a API key do Anthropic para análises avançadas"
            }
        
        # Prepare context for AI
        results_context = "\n".join([
            f"- {result.test_name}: {result.value} {result.unit} (Status: {result.status}, Ref: {result.reference_range})"
            for result in lab_results
        ])
        
        prompt = f"""
        Analise os seguintes resultados de exames laboratoriais em português:

        RESULTADOS:
        {results_context}

        TEXTO ORIGINAL:
        {text[:1000]}...

        Por favor, forneça:
        1. Um resumo clínico conciso dos resultados
        2. Identificação de valores alterados e sua significância clínica
        3. Recomendações gerais (sempre mencionar que é necessário consultar um médico)

        Responda em português brasileiro, de forma profissional e clara.
        """
        
        try:
            response = self.client.messages.create(
                model="claude-sonnet-4-20250514",
                max_tokens=64000,
                temperature=0.1,
                messages=[{"role": "user", "content": prompt}]
            )
            
            ai_response = response.content[0].text
            
            # Split response into summary and recommendations
            parts = ai_response.split("Recomendações")
            summary = parts[0].strip()
            recommendations = "Recomendações" + parts[1].strip() if len(parts) > 1 else "Consulte um médico para interpretação adequada."
            
            return {
                "summary": summary,
                "recommendations": recommendations
            }
            
        except Exception as e:
            return {
                "summary": f"Erro na análise de IA: {str(e)}",
                "recommendations": "Consulte um médico para interpretação adequada dos resultados."
            }

    def format_results(self, lab_results: List[LabResult]) -> str:
        """Format lab results into clinical text"""
        if not lab_results:
            return "Nenhum resultado encontrado."

        # Group by category
        categories = {}
        for result in lab_results:
            if result.category not in categories:
                categories[result.category] = []
            categories[result.category].append(result)

        formatted_lines = []

        # Format each category
        for category, results in categories.items():
            if category == 'hemograma':
                line = "HEMOGRAMA: "
                parts = []
                for result in results:
                    if result.test_name.lower() == 'hemoglobina':
                        parts.append(f"Hb {result.value}g/dL")
                    elif result.test_name.lower() == 'hematocrito':
                        parts.append(f"Ht {result.value}%")
                    elif 'eritrocitos' in result.test_name.lower():
                        parts.append(f"Hem {result.value}")
                    elif 'leucocitos' in result.test_name.lower():
                        parts.append(f"Leuc {result.value}")
                    elif 'plaquetas' in result.test_name.lower():
                        parts.append(f"Plaq {result.value}")
                line += " / ".join(parts)
                formatted_lines.append(line)

            elif category == 'bioquimica':
                line = "BIOQUÍMICA: "
                parts = []
                for result in results:
                    if 'glicose' in result.test_name.lower():
                        parts.append(f"Gli {result.value}mg/dL")
                    elif 'creatinina' in result.test_name.lower():
                        parts.append(f"Cr {result.value}mg/dL")
                    elif 'ureia' in result.test_name.lower():
                        parts.append(f"Ureia {result.value}mg/dL")
                    elif 'colesterol' in result.test_name.lower():
                        parts.append(f"CT {result.value}mg/dL")
                line += " / ".join(parts)
                formatted_lines.append(line)

            elif category == 'hepatico':
                line = "FUNÇÃO HEPÁTICA: "
                parts = []
                for result in results:
                    if 'alt' in result.test_name.lower() or 'tgp' in result.test_name.lower():
                        parts.append(f"ALT {result.value}U/L")
                    elif 'ast' in result.test_name.lower() or 'tgo' in result.test_name.lower():
                        parts.append(f"AST {result.value}U/L")
                    elif 'bilirrubina' in result.test_name.lower():
                        parts.append(f"BT {result.value}mg/dL")
                line += " / ".join(parts)
                formatted_lines.append(line)

            elif category == 'tireoide':
                line = "TIREOIDE: "
                parts = []
                for result in results:
                    if 'tsh' in result.test_name.lower():
                        parts.append(f"TSH {result.value}mUI/L")
                    elif 't4' in result.test_name.lower():
                        parts.append(f"T4L {result.value}ng/dL")
                    elif 't3' in result.test_name.lower():
                        parts.append(f"T3L {result.value}pg/mL")
                line += " / ".join(parts)
                formatted_lines.append(line)

        return "\n".join(formatted_lines)

    def create_alerts(self, lab_results: List[LabResult]) -> List[Dict]:
        """Create alerts for abnormal values"""
        alerts = []

        for result in lab_results:
            if result.status in ['high', 'low']:
                alert = {
                    'test': result.test_name,
                    'value': f"{result.value} {result.unit}",
                    'status': result.status,
                    'reference': result.reference_range,
                    'severity': 'warning' if result.status in ['high', 'low'] else 'critical'
                }
                alerts.append(alert)

        return alerts

    def process_input(self, text: str = None, file_data: Any = None, file_type: str = None) -> ProcessedResults:
        """Process input text or file and return formatted results"""

        # Extract text from different sources
        if file_data and file_type:
            if file_type == 'pdf':
                text = self.extract_text_from_pdf(file_data)
            elif file_type in ['png', 'jpg', 'jpeg']:
                text = self.extract_text_from_image(file_data)

        if not text:
            return ProcessedResults(
                formatted_text="Nenhum texto encontrado para processar.",
                alerts=[],
                raw_data=[],
                summary="Erro: Texto vazio",
                recommendations="Verifique o arquivo ou texto fornecido."
            )

        # Parse lab results
        lab_results = self.parser.parse_text(text)

        # Format results
        formatted_text = self.format_results(lab_results)

        # Create alerts
        alerts = self.create_alerts(lab_results)

        # Get AI analysis
        ai_analysis = self.get_ai_analysis(text, lab_results)

        return ProcessedResults(
            formatted_text=formatted_text,
            alerts=alerts,
            raw_data=lab_results,
            summary=ai_analysis['summary'],
            recommendations=ai_analysis['recommendations']
        )

def main():
    """Main Streamlit application"""

    # Page configuration
    st.set_page_config(
        page_title="AI Labs Formatter Pro",
        page_icon="🧪",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Custom CSS
    st.markdown("""
    <style>
    .main-header {
        background: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .alert-box {
        padding: 1rem;
        border-radius: 8px;
        margin: 0.5rem 0;
    }
    .alert-high {
        background-color: #fee2e2;
        border-left: 4px solid #dc2626;
        color: #991b1b;
    }
    .alert-low {
        background-color: #dbeafe;
        border-left: 4px solid #2563eb;
        color: #1d4ed8;
    }
    .success-box {
        background-color: #d1fae5;
        border-left: 4px solid #10b981;
        color: #065f46;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)

    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🧪 AI Labs Formatter Pro</h1>
        <p>Formatação Inteligente de Exames Laboratoriais com IA</p>
    </div>
    """, unsafe_allow_html=True)

    # Sidebar for configuration
    with st.sidebar:
        st.header("⚙️ Configurações")

        # API Key input
        api_key = st.text_input(
            "Anthropic API Key (opcional)",
            type="password",
            help="Para análises avançadas com IA. Deixe vazio para usar apenas formatação básica."
        )

        st.markdown("---")

        # Instructions
        st.markdown("""
        ### 📋 Como usar:
        1. **Texto**: Cole o resultado dos exames
        2. **PDF**: Faça upload de arquivos PDF
        3. **Imagem**: Faça upload de imagens (OCR)
        4. Clique em "Processar" para formatar

        ### ⚠️ Importante:
        - Sempre revise os resultados
        - Consulte um médico para interpretação
        - Esta ferramenta é apenas auxiliar
        """)

    # Initialize formatter
    formatter = AILabFormatter(api_key=api_key)

    # Main interface
    col1, col2 = st.columns([1, 1])

    with col1:
        st.subheader("📝 Entrada de Dados")

        # Input method selection
        input_method = st.radio(
            "Escolha o método de entrada:",
            ["Texto", "Upload de Arquivo"],
            horizontal=True
        )

        text_input = None
        file_data = None
        file_type = None

        if input_method == "Texto":
            text_input = st.text_area(
                "Cole aqui o resultado dos exames:",
                height=300,
                placeholder="""Exemplo:
HEMOGRAMA COMPLETO
Hemoglobina: 14.2 g/dL
Hematócrito: 42%
Leucócitos: 7.500/mm³
Plaquetas: 280.000/mm³

BIOQUÍMICA SÉRICA
Glicose: 92 mg/dL
Creatinina: 0.9 mg/dL
Uréia: 28 mg/dL"""
            )

        else:
            uploaded_file = st.file_uploader(
                "Escolha um arquivo:",
                type=['pdf', 'png', 'jpg', 'jpeg'],
                help="Suporte para PDF e imagens (PNG, JPG)"
            )

            if uploaded_file:
                file_data = uploaded_file
                file_type = uploaded_file.type.split('/')[-1]
                if file_type == 'jpeg':
                    file_type = 'jpg'

                st.success(f"Arquivo carregado: {uploaded_file.name}")

        # Process button
        if st.button("🚀 Processar Exames", type="primary", use_container_width=True):
            if text_input or file_data:
                with st.spinner("Processando exames..."):
                    try:
                        results = formatter.process_input(
                            text=text_input,
                            file_data=file_data,
                            file_type=file_type
                        )

                        # Store results in session state
                        st.session_state['results'] = results
                        st.success("✅ Exames processados com sucesso!")

                    except Exception as e:
                        st.error(f"❌ Erro ao processar: {str(e)}")
            else:
                st.warning("⚠️ Por favor, forneça texto ou faça upload de um arquivo.")

    with col2:
        st.subheader("📊 Resultados")

        if 'results' in st.session_state:
            results = st.session_state['results']

            # Formatted results
            st.markdown("### 📋 Resultado Formatado")
            st.text_area(
                "Resultado formatado:",
                value=results.formatted_text,
                height=200,
                key="formatted_output"
            )

            # Copy button
            if st.button("📋 Copiar Resultado"):
                st.code(results.formatted_text, language=None)
                st.info("💡 Use Ctrl+A e Ctrl+C para copiar o texto acima")

            # Alerts section
            if results.alerts:
                st.markdown("### ⚠️ Valores Alterados")
                for alert in results.alerts:
                    alert_class = "alert-high" if alert['status'] == 'high' else "alert-low"
                    status_text = "ALTO" if alert['status'] == 'high' else "BAIXO"

                    st.markdown(f"""
                    <div class="alert-box {alert_class}">
                        <strong>{alert['test']}:</strong> {alert['value']} - {status_text}<br>
                        <small>Referência: {alert['reference']}</small>
                    </div>
                    """, unsafe_allow_html=True)

            # AI Analysis (if available)
            if api_key and results.summary:
                st.markdown("### 🤖 Análise de IA")

                with st.expander("📊 Resumo Clínico", expanded=True):
                    st.write(results.summary)

                with st.expander("💡 Recomendações"):
                    st.write(results.recommendations)

            # Raw data table
            if results.raw_data:
                st.markdown("### 📈 Dados Detalhados")

                df_data = []
                for result in results.raw_data:
                    df_data.append({
                        'Exame': result.test_name,
                        'Valor': result.value,
                        'Unidade': result.unit,
                        'Referência': result.reference_range,
                        'Status': result.status.upper()
                    })

                df = pd.DataFrame(df_data)
                st.dataframe(df, use_container_width=True)

        else:
            st.info("👆 Processe alguns exames para ver os resultados aqui.")

    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
        <p>🏥 <strong>AI Labs Formatter Pro</strong> - Ferramenta auxiliar para formatação de exames</p>
        <p>⚠️ <em>Sempre consulte um médico para interpretação adequada dos resultados</em></p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
